2025-04-14 07:54:27.446155-04:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "MIGRATION_MODEL" not found (this database is empty); SQL statement:
SELECT ID, VERSION FROM MIGRATION_MODEL ORDER BY UPDATE_TIME DESC [42104-230]
2025-04-14 07:54:28.473000-04:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "DATABASECHANGELOG" not found (this database is empty); SQL statement:
SELECT COUNT(*) FROM PUBLIC.DATABASECHANGELOG [42104-230]
2025-04-14 07:54:28.845655-04:00 jdbc[4]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "DATABASECHANGELOGLOCK" not found (this database is empty); SQL statement:
SELECT COUNT(*) FROM PUBLIC.DATABASECHANGELOGLOCK [42104-230]
2025-04-14 07:54:28.896786-04:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "DATABASECHANGELOG" not found; SQL statement:
SELECT COUNT(*) FROM PUBLIC.DATABASECHANGELOG [42102-230]
2025-04-14 07:54:33.887744-04:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLSyntaxErrorException: Column "T.DETAILS_JSON" not found; SQL statement:
select t.DETAILS_JSON from PUBLIC.ADMIN_EVENT_ENTITY t where 0=1 [42122-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.expression.ExpressionColumn.getColumnException(ExpressionColumn.java:244)
	at org.h2.expression.ExpressionColumn.optimizeOther(ExpressionColumn.java:226)
	at org.h2.expression.ExpressionColumn.optimize(ExpressionColumn.java:213)
	at org.h2.command.query.Select.prepareExpressions(Select.java:1228)
	at org.h2.command.query.Query.prepare(Query.java:232)
	at org.h2.command.Parser.prepareCommand(Parser.java:489)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:644)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:560)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:315)
	at io.agroal.pool.wrapper.ConnectionWrapper.prepareStatement(ConnectionWrapper.java:647)
	at liquibase.database.jvm.JdbcConnection.prepareStatement(JdbcConnection.java:443)
	at liquibase.precondition.core.ColumnExistsPrecondition.makeSureColumnExistsInOtherDBs(ColumnExistsPrecondition.java:161)
	at liquibase.precondition.core.ColumnExistsPrecondition.checkFast(ColumnExistsPrecondition.java:140)
	at liquibase.precondition.core.ColumnExistsPrecondition.check(ColumnExistsPrecondition.java:83)
	at liquibase.precondition.core.NotPrecondition.check(NotPrecondition.java:34)
	at liquibase.precondition.core.AndPrecondition.check(AndPrecondition.java:39)
	at liquibase.precondition.core.PreconditionContainer.check(PreconditionContainer.java:213)
	at liquibase.changelog.ChangeSet.execute(ChangeSet.java:689)
	at liquibase.changelog.visitor.UpdateVisitor.executeAcceptedChange(UpdateVisitor.java:119)
	at liquibase.changelog.visitor.UpdateVisitor.visit(UpdateVisitor.java:68)
	at liquibase.changelog.ChangeLogIterator.lambda$run$0(ChangeLogIterator.java:131)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.changelog.ChangeLogIterator.lambda$run$1(ChangeLogIterator.java:120)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.Scope.child(Scope.java:257)
	at liquibase.Scope.child(Scope.java:261)
	at liquibase.changelog.ChangeLogIterator.run(ChangeLogIterator.java:89)
	at liquibase.command.core.AbstractUpdateCommandStep.lambda$run$0(AbstractUpdateCommandStep.java:113)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.command.core.AbstractUpdateCommandStep.run(AbstractUpdateCommandStep.java:111)
	at liquibase.command.core.UpdateCommandStep.run(UpdateCommandStep.java:105)
	at liquibase.command.CommandScope.execute(CommandScope.java:220)
	at liquibase.Liquibase.lambda$update$0(Liquibase.java:216)
	at liquibase.Scope.lambda$child$0(Scope.java:191)
	at liquibase.Scope.child(Scope.java:200)
	at liquibase.Scope.child(Scope.java:190)
	at liquibase.Scope.child(Scope.java:169)
	at liquibase.Liquibase.runInScope(Liquibase.java:1290)
	at liquibase.Liquibase.update(Liquibase.java:205)
	at liquibase.Liquibase.update(Liquibase.java:188)
	at liquibase.Liquibase.update(Liquibase.java:175)
	at org.keycloak.quarkus.runtime.storage.database.liquibase.QuarkusJpaUpdaterProvider.updateChangeSet(QuarkusJpaUpdaterProvider.java:190)
	at org.keycloak.quarkus.runtime.storage.database.liquibase.QuarkusJpaUpdaterProvider.update(QuarkusJpaUpdaterProvider.java:105)
	at org.keycloak.quarkus.runtime.storage.database.liquibase.QuarkusJpaUpdaterProvider.update(QuarkusJpaUpdaterProvider.java:83)
	at org.keycloak.quarkus.runtime.storage.database.jpa.QuarkusJpaConnectionProviderFactory.update(QuarkusJpaConnectionProviderFactory.java:281)
	at org.keycloak.quarkus.runtime.storage.database.jpa.QuarkusJpaConnectionProviderFactory.createOrUpdateSchema(QuarkusJpaConnectionProviderFactory.java:247)
	at org.keycloak.quarkus.runtime.storage.database.jpa.QuarkusJpaConnectionProviderFactory.postInit(QuarkusJpaConnectionProviderFactory.java:124)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:168)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initializeProviders(DefaultKeycloakSessionFactory.java:165)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initProviderFactories(DefaultKeycloakSessionFactory.java:145)
	at org.keycloak.services.DefaultKeycloakSessionFactory.initProviderFactories(DefaultKeycloakSessionFactory.java:126)
	at org.keycloak.quarkus.runtime.integration.QuarkusKeycloakSessionFactory.init(QuarkusKeycloakSessionFactory.java:87)
	at org.keycloak.quarkus.runtime.integration.jaxrs.QuarkusKeycloakApplication.createSessionFactory(QuarkusKeycloakApplication.java:67)
	at org.keycloak.services.resources.KeycloakApplication.startup(KeycloakApplication.java:90)
	at org.keycloak.quarkus.runtime.integration.jaxrs.QuarkusKeycloakApplication.onStartupEvent(QuarkusKeycloakApplication.java:52)
	at org.keycloak.quarkus.runtime.integration.jaxrs.QuarkusKeycloakApplication_Observer_onStartupEvent_GNZ8m5QenZ9h9VNelo7awjUZFDE.notify(Unknown Source)
	at io.quarkus.arc.impl.EventImpl$Notifier.notifyObservers(EventImpl.java:365)
	at io.quarkus.arc.impl.EventImpl$Notifier.notify(EventImpl.java:347)
	at io.quarkus.arc.impl.EventImpl.fire(EventImpl.java:81)
	at io.quarkus.arc.runtime.ArcRecorder.fireLifecycleEvent(ArcRecorder.java:163)
	at io.quarkus.arc.runtime.ArcRecorder.handleLifecycleEvents(ArcRecorder.java:114)
	at io.quarkus.runner.recorded.LifecycleEventsBuildStep$startupEvent1144526294.deploy_0(Unknown Source)
	at io.quarkus.runner.recorded.LifecycleEventsBuildStep$startupEvent1144526294.deploy(Unknown Source)
	at io.quarkus.runner.ApplicationImpl.doStart(Unknown Source)
	at io.quarkus.runtime.Application.start(Application.java:101)
	at io.quarkus.runtime.ApplicationLifecycleManager.run(ApplicationLifecycleManager.java:121)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:77)
	at org.keycloak.quarkus.runtime.KeycloakMain.start(KeycloakMain.java:145)
	at org.keycloak.quarkus.runtime.cli.Picocli.start(Picocli.java:988)
	at org.keycloak.quarkus.runtime.cli.command.AbstractStartCommand.run(AbstractStartCommand.java:49)
	at picocli.CommandLine.executeUserObject(CommandLine.java:2030)
	at picocli.CommandLine.access$1500(CommandLine.java:148)
	at picocli.CommandLine$RunLast.executeUserObjectOfLastSubcommandWithSameParent(CommandLine.java:2465)
	at picocli.CommandLine$RunLast.handle(CommandLine.java:2457)
	at picocli.CommandLine$RunLast.handle(CommandLine.java:2419)
	at picocli.CommandLine$AbstractParseResultHandler.execute(CommandLine.java:2277)
	at picocli.CommandLine$RunLast.execute(CommandLine.java:2421)
	at picocli.CommandLine.execute(CommandLine.java:2174)
	at org.keycloak.quarkus.runtime.cli.Picocli.parseAndRun(Picocli.java:128)
	at org.keycloak.quarkus.runtime.KeycloakMain.main(KeycloakMain.java:116)
	at org.keycloak.quarkus.runtime.KeycloakMain.main(KeycloakMain.java:71)
	at io.quarkus.bootstrap.runner.QuarkusEntryPoint.doRun(QuarkusEntryPoint.java:68)
	at io.quarkus.bootstrap.runner.QuarkusEntryPoint.main(QuarkusEntryPoint.java:36)
2025-05-28 03:47:39.899496-04:00 jdbc[3]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'[{""AppName"":""iRISupplyWeb 5.0"",""AppLink"":""https://cch9appvm01.mobileaspectsheal... (491)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 03:47:39.918493-04:00 jdbc[3]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'[{""AppName"":""iRISupplyWeb 5.0"",""AppLink"":""https://cch9appvm01.mobileaspectsheal... (491)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'[{""AppName"":""iRISupplyWeb 5.0"",""AppLink"":""https://cch9appvm01.mobileaspectsheal... (491)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 04:34:50.027769-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'{""AppName"":""iRISupplyWeb 5.0"",""AppLink"":""https://cch9appvm01.mobileaspectshealt... (489)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 04:34:50.028768-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'{""AppName"":""iRISupplyWeb 5.0"",""AppLink"":""https://cch9appvm01.mobileaspectshealt... (489)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'{""AppName"":""iRISupplyWeb 5.0"",""AppLink"":""https://cch9appvm01.mobileaspectshealt... (489)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 04:38:14.878422-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'While Keycloak''s official documentation doesn''t explicitly state a maximum ch... (1863)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 04:38:14.879420-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'While Keycloak''s official documentation doesn''t explicitly state a maximum ch... (1863)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'While Keycloak''s official documentation doesn''t explicitly state a maximum ch... (1863)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 04:40:41.095858-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'[{""action"": ""read"", ""resource"": ""document""}, {""action"": ""write"", ""resource"": ""a... (655)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 04:40:41.095858-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'[{""action"": ""read"", ""resource"": ""document""}, {""action"": ""write"", ""resource"": ""a... (655)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'[{""action"": ""read"", ""resource"": ""document""}, {""action"": ""write"", ""resource"": ""a... (655)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 04:48:48.025360-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (263)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 04:48:48.026357-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (263)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (263)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 04:48:55.504571-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (263)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 04:48:55.504571-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (263)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (263)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 04:49:09.349058-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (262)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 04:49:09.349058-04:00 jdbc[46]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (262)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa... (262)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultAutoFlushEventListener.onAutoFlush(DefaultAutoFlushEventListener.java:67)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1379)
	at org.hibernate.internal.SessionImpl.autoFlushIfRequired(SessionImpl.java:1366)
	at org.hibernate.sql.exec.internal.StandardJdbcMutationExecutor.execute(StandardJdbcMutationExecutor.java:46)
	at org.hibernate.query.sqm.internal.SimpleDeleteQueryPlan.executeUpdate(SimpleDeleteQueryPlan.java:187)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doExecuteUpdate(QuerySqmImpl.java:521)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.executeUpdate(QuerySqmImpl.java:493)
	at org.keycloak.models.jpa.RoleAdapter.removeAttribute(RoleAdapter.java:159)
	at org.keycloak.models.jpa.RoleAdapter.setAttribute(RoleAdapter.java:145)
	at org.keycloak.models.cache.infinispan.RoleAdapter.setAttribute(RoleAdapter.java:206)
	at org.keycloak.services.resources.admin.RoleResource.updateRole(RoleResource.java:107)
	at org.keycloak.services.resources.admin.RoleByIdResource.updateRole(RoleByIdResource.java:170)
	at org.keycloak.services.resources.admin.RoleByIdResource$quarkusrestinvoker$updateRole_32a2b1c0a9ce48ef6654669dba5f1d53a58900d7.invoke(Unknown Source)
	at org.jboss.resteasy.reactive.server.handlers.InvocationHandler.handle(InvocationHandler.java:29)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:141)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-05-28 07:41:06.528066-04:00 jdbc[48]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'{""AppName"":[""iRISupplyWeb 5.0""],""AppLink"":[""https://cch9appvm01.mobileaspectshe... (256)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
2025-05-28 07:41:06.529073-04:00 jdbc[48]: exception
org.h2.jdbc.JdbcBatchUpdateException: Value too long for column "VALUE CHARACTER VARYING(255)": "'{""AppName"":[""iRISupplyWeb 5.0""],""AppLink"":[""https://cch9appvm01.mobileaspectshe... (256)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1277)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorImpl.beforeCompletion(JtaTransactionCoordinatorImpl.java:336)
	at org.hibernate.resource.transaction.backend.jta.internal.synchronization.SynchronizationCallbackCoordinatorNonTrackingImpl.beforeCompletion(SynchronizationCallbackCoordinatorNonTrackingImpl.java:47)
	at org.hibernate.resource.transaction.backend.jta.internal.synchronization.RegisteredSynchronization.beforeCompletion(RegisteredSynchronization.java:37)
	at com.arjuna.ats.internal.jta.resources.arjunacore.SynchronizationImple.beforeCompletion(SynchronizationImple.java:52)
	at com.arjuna.ats.arjuna.coordinator.TwoPhaseCoordinator.beforeCompletion(TwoPhaseCoordinator.java:348)
	at com.arjuna.ats.arjuna.coordinator.TwoPhaseCoordinator.end(TwoPhaseCoordinator.java:66)
	at com.arjuna.ats.arjuna.AtomicAction.commit(AtomicAction.java:135)
	at com.arjuna.ats.internal.jta.transaction.arjunacore.TransactionImple.commitAndDisassociate(TransactionImple.java:1307)
	at com.arjuna.ats.internal.jta.transaction.arjunacore.BaseTransaction.commit(BaseTransaction.java:104)
	at io.quarkus.narayana.jta.runtime.NotifyingTransactionManager.commit(NotifyingTransactionManager.java:70)
	at org.keycloak.transaction.JtaTransactionWrapper.commit(JtaTransactionWrapper.java:102)
	at org.keycloak.services.DefaultKeycloakTransactionManager.lambda$commitWithTracing$0(DefaultKeycloakTransactionManager.java:169)
	at org.keycloak.tracing.NoopTracingProvider.trace(NoopTracingProvider.java:59)
	at org.keycloak.tracing.NoopTracingProvider.trace(NoopTracingProvider.java:69)
	at org.keycloak.services.DefaultKeycloakTransactionManager.commitWithTracing(DefaultKeycloakTransactionManager.java:168)
	at org.keycloak.services.DefaultKeycloakTransactionManager.commit(DefaultKeycloakTransactionManager.java:136)
	at org.keycloak.services.DefaultKeycloakSession.closeTransactionManager(DefaultKeycloakSession.java:396)
	at org.keycloak.services.DefaultKeycloakSession.close(DefaultKeycloakSession.java:361)
	at org.keycloak.models.KeycloakBeanProducer_ProducerMethod_getKeycloakSession_XoSEUTXOsE3bpqXlGMAykCiECUM_ClientProxy.close(Unknown Source)
	at org.keycloak.quarkus.runtime.transaction.TransactionalSessionHandler.close(TransactionalSessionHandler.java:60)
	at org.keycloak.quarkus.runtime.integration.jaxrs.CloseSessionFilter.closeSession(CloseSessionFilter.java:67)
	at org.keycloak.quarkus.runtime.integration.jaxrs.CloseSessionFilter.filter(CloseSessionFilter.java:63)
	at org.jboss.resteasy.reactive.server.handlers.ResourceResponseFilterHandler.handle(ResourceResponseFilterHandler.java:25)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:150)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
org.h2.jdbc.JdbcSQLDataException: Value too long for column "VALUE CHARACTER VARYING(255)": "'{""AppName"":[""iRISupplyWeb 5.0""],""AppLink"":[""https://cch9appvm01.mobileaspectshe... (256)"; SQL statement:
insert into ROLE_ATTRIBUTE (NAME,ROLE_ID,VALUE,ID) values (?,?,?,?) [22001-230]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:518)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.getValueTooLongException(DbException.java:334)
	at org.h2.value.Value.getValueTooLongException(Value.java:2612)
	at org.h2.value.Value.convertToVarchar(Value.java:1287)
	at org.h2.value.Value.convertTo(Value.java:1143)
	at org.h2.value.Value.convertForAssignTo(Value.java:1118)
	at org.h2.table.Column.validateConvertUpdateSequence(Column.java:406)
	at org.h2.table.Table.convertInsertRow(Table.java:963)
	at org.h2.command.dml.Insert.insertRows(Insert.java:167)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:139)
	at org.h2.command.Command.executeUpdate(Command.java:304)
	at org.h2.command.Command.executeBatchUpdate(Command.java:271)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchInternal(JdbcPreparedStatement.java:1318)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1267)
	at io.agroal.pool.wrapper.StatementWrapper.executeBatch(StatementWrapper.java:340)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.lambda$performExecution$2(BatchImpl.java:279)
	at org.hibernate.engine.jdbc.mutation.internal.PreparedStatementGroupSingleTable.forEachStatement(PreparedStatementGroupSingleTable.java:67)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.performExecution(BatchImpl.java:264)
	at org.hibernate.engine.jdbc.batch.internal.BatchImpl.execute(BatchImpl.java:242)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.executeBatch(JdbcCoordinatorImpl.java:188)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:674)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jta.internal.JtaTransactionCoordinatorImpl.beforeCompletion(JtaTransactionCoordinatorImpl.java:336)
	at org.hibernate.resource.transaction.backend.jta.internal.synchronization.SynchronizationCallbackCoordinatorNonTrackingImpl.beforeCompletion(SynchronizationCallbackCoordinatorNonTrackingImpl.java:47)
	at org.hibernate.resource.transaction.backend.jta.internal.synchronization.RegisteredSynchronization.beforeCompletion(RegisteredSynchronization.java:37)
	at com.arjuna.ats.internal.jta.resources.arjunacore.SynchronizationImple.beforeCompletion(SynchronizationImple.java:52)
	at com.arjuna.ats.arjuna.coordinator.TwoPhaseCoordinator.beforeCompletion(TwoPhaseCoordinator.java:348)
	at com.arjuna.ats.arjuna.coordinator.TwoPhaseCoordinator.end(TwoPhaseCoordinator.java:66)
	at com.arjuna.ats.arjuna.AtomicAction.commit(AtomicAction.java:135)
	at com.arjuna.ats.internal.jta.transaction.arjunacore.TransactionImple.commitAndDisassociate(TransactionImple.java:1307)
	at com.arjuna.ats.internal.jta.transaction.arjunacore.BaseTransaction.commit(BaseTransaction.java:104)
	at io.quarkus.narayana.jta.runtime.NotifyingTransactionManager.commit(NotifyingTransactionManager.java:70)
	at org.keycloak.transaction.JtaTransactionWrapper.commit(JtaTransactionWrapper.java:102)
	at org.keycloak.services.DefaultKeycloakTransactionManager.lambda$commitWithTracing$0(DefaultKeycloakTransactionManager.java:169)
	at org.keycloak.tracing.NoopTracingProvider.trace(NoopTracingProvider.java:59)
	at org.keycloak.tracing.NoopTracingProvider.trace(NoopTracingProvider.java:69)
	at org.keycloak.services.DefaultKeycloakTransactionManager.commitWithTracing(DefaultKeycloakTransactionManager.java:168)
	at org.keycloak.services.DefaultKeycloakTransactionManager.commit(DefaultKeycloakTransactionManager.java:136)
	at org.keycloak.services.DefaultKeycloakSession.closeTransactionManager(DefaultKeycloakSession.java:396)
	at org.keycloak.services.DefaultKeycloakSession.close(DefaultKeycloakSession.java:361)
	at org.keycloak.models.KeycloakBeanProducer_ProducerMethod_getKeycloakSession_XoSEUTXOsE3bpqXlGMAykCiECUM_ClientProxy.close(Unknown Source)
	at org.keycloak.quarkus.runtime.transaction.TransactionalSessionHandler.close(TransactionalSessionHandler.java:60)
	at org.keycloak.quarkus.runtime.integration.jaxrs.CloseSessionFilter.closeSession(CloseSessionFilter.java:67)
	at org.keycloak.quarkus.runtime.integration.jaxrs.CloseSessionFilter.filter(CloseSessionFilter.java:63)
	at org.jboss.resteasy.reactive.server.handlers.ResourceResponseFilterHandler.handle(ResourceResponseFilterHandler.java:25)
	at io.quarkus.resteasy.reactive.server.runtime.QuarkusResteasyReactiveRequestContext.invokeHandler(QuarkusResteasyReactiveRequestContext.java:150)
	at org.jboss.resteasy.reactive.common.core.AbstractResteasyReactiveContext.run(AbstractResteasyReactiveContext.java:147)
	at io.quarkus.vertx.core.runtime.VertxCoreRecorder$15.runWith(VertxCoreRecorder.java:638)
	at org.jboss.threads.EnhancedQueueExecutor$Task.doRunWith(EnhancedQueueExecutor.java:2675)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2654)
	at org.jboss.threads.EnhancedQueueExecutor.runThreadBody(EnhancedQueueExecutor.java:1627)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1594)
	at org.jboss.threads.DelegatingRunnable.run(DelegatingRunnable.java:11)
	at org.jboss.threads.ThreadLocalResettingRunnable.run(ThreadLocalResettingRunnable.java:11)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
