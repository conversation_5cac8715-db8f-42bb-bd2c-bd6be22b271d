@echo off
rem Script to export realm configurations from both databases for comparison

echo Exporting realm configurations for comparison...

set KEYCLOAK_DIR=%~dp0
set KCADM=%KEYCLOAK_DIR%bin\kcadm.bat

echo.
echo This script will help you export realm configurations from both databases.
echo You'll need to:
echo 1. First connect to MA_ALL_MAH9_IRISIDPDB database
echo 2. Export the realm configuration
echo 3. Then connect to MAA-ALL-CCH1CV-IRISIAMDB database  
echo 4. Export the realm configuration
echo 5. Compare the two files

echo.
echo Step 1: Make sure Key<PERSON>loak is running with MA_ALL_MAH9_IRISIDPDB database
echo Press any key when ready to export from MA_ALL_MAH9_IRISIDPDB...
pause

echo.
echo Logging into Keycloak (MA_ALL_MAH9_IRISIDPDB)...
%KCADM% config credentials --server https://************:9443 --realm master --user admin

echo.
echo Exporting realm configuration from MA_ALL_MAH9_IRISIDPDB...
%KCADM% get realms/master > realm_config_MAH9.json

echo.
echo Export from MA_ALL_MAH9_IRISIDPDB completed: realm_config_MAH9.json

echo.
echo Step 2: Now switch Keycloak to MAA-ALL-CCH1CV-IRISIAMDB database
echo 1. Stop Keycloak
echo 2. Update keycloak.conf to use MAA-ALL-CCH1CV-IRISIAMDB
echo 3. Start Keycloak
echo Press any key when ready to export from MAA-ALL-CCH1CV-IRISIAMDB...
pause

echo.
echo Logging into Keycloak (MAA-ALL-CCH1CV-IRISIAMDB)...
%KCADM% config credentials --server https://************:9443 --realm master --user admin

echo.
echo Exporting realm configuration from MAA-ALL-CCH1CV-IRISIAMDB...
%KCADM% get realms/master > realm_config_CCH1CV.json

echo.
echo Export from MAA-ALL-CCH1CV-IRISIAMDB completed: realm_config_CCH1CV.json

echo.
echo Both realm configurations have been exported:
echo - realm_config_MAH9.json (from MA_ALL_MAH9_IRISIDPDB)
echo - realm_config_CCH1CV.json (from MAA-ALL-CCH1CV-IRISIAMDB)
echo.
echo Compare these files to find differences in theme settings.
echo Look for differences in: loginTheme, accountTheme, adminTheme, emailTheme

pause
