@echo off
rem <PERSON>rip<PERSON> to check and fix Keycloak theme configuration in database
rem This script will check the current theme settings and update them if needed

echo Checking Keycloak theme configuration...

rem Set variables
set KEYCLOAK_DIR=%~dp0
set KCADM=%KEYCLOAK_DIR%bin\kcadm.bat
set REALM_NAME=master

echo.
echo Step 1: Logging into Keycloak Admin CLI...
echo Please enter your admin credentials when prompted.

rem Login to Keycloak (you'll need to provide admin credentials)
%KCADM% config credentials --server https://************:9443 --realm master --user admin

echo.
echo Step 2: Checking current realm theme settings...

rem Get current realm configuration
%KCADM% get realms/%REALM_NAME% --fields loginTheme,accountTheme,adminTheme,emailTheme

echo.
echo Step 3: Updating login theme to 'socialaccountsonly'...

rem Update the login theme
%KCADM% update realms/%REALM_NAME% -s loginTheme=socialaccountsonly

echo.
echo Step 4: Verifying the theme update...

rem Verify the update
%KCADM% get realms/%REALM_NAME% --fields loginTheme

echo.
echo Theme configuration update completed!
echo Please restart Keycloak and test the Microsoft button styling.

pause
