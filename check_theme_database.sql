-- SQL script to check Keycloak theme configuration in the database
-- Run this against both databases to compare theme settings

-- Check realm theme settings
SELECT 
    NAME as REALM_NAME,
    LOGIN_THEME,
    ACCOUNT_THEME,
    ADMIN_THEME,
    EMAIL_THEME
FROM REALM
ORDER BY NAME;

-- Check realm attributes that might affect themes
SELECT 
    r.NAME as REALM_NAME,
    ra.NAME as ATTRIBUTE_NAME,
    ra.VALUE as ATTRIBUTE_VALUE
FROM REALM r
LEFT JOIN REALM_ATTRIBUTE ra ON r.ID = ra.REALM_ID
WHERE ra.NAME LIKE '%theme%' OR ra.NAME LIKE '%style%' OR ra.NAME LIKE '%css%'
ORDER BY r.NAME, ra.NAME;

-- Check if there are any client-specific theme overrides
SELECT 
    c.CLIENT_ID,
    ca.NAME as ATTRIBUTE_NAME,
    ca.VALUE as ATTRIBUTE_VALUE
FROM CLIENT c
LEFT JOIN CLIENT_ATTRIBUTES ca ON c.ID = ca.CLIENT_ID
WHERE ca.NAME LIKE '%theme%' OR ca.NAME LIKE '%style%' OR ca.NAME LIKE '%css%'
ORDER BY c.CLIENT_ID, ca.NAME;

-- Check identity provider configurations that might affect styling
SELECT
    INTERNAL_ID,
    PROVIDER_ID,
    PROVIDER_DISPLAY_NAME,
    ENABLED
FROM IDENTITY_PROVIDER
ORDER BY PROVIDER_ID;

-- Check identity provider mappers that might affect button styling
SELECT
    ip.INTERNAL_ID,
    ip.PROVIDER_ID,
    ipm.NAME as MAPPER_NAME,
    ipm.IDENTITY_PROVIDER_MAPPER as MAPPER_TYPE
FROM IDENTITY_PROVIDER ip
LEFT JOIN IDENTITY_PROVIDER_MAPPER ipm ON ip.INTERNAL_ID = ipm.IDP_ID
WHERE ip.PROVIDER_ID = 'microsoft' OR ip.PROVIDER_ID LIKE '%microsoft%'
ORDER BY ip.PROVIDER_ID, ipm.NAME;
