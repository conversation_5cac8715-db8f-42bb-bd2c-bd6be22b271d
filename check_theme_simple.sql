-- Simple SQL script to check Keycloak theme configuration
-- Run this against both databases to compare theme settings

-- Check realm theme settings (most important)
SELECT 
    NAME as REALM_NAME,
    LOGIN_THEME,
    ACCOUNT_THEME,
    ADMIN_THEME,
    EMAIL_THEME
FROM REALM
ORDER BY NAME;

-- Check realm attributes for any theme-related settings
SELECT 
    r.NAME as REALM_NAME,
    ra.NAME as ATTRIBUTE_NAME,
    ra.VALUE as ATTRIBUTE_VALUE
FROM REALM r
LEFT JOIN REALM_ATTRIBUTE ra ON r.ID = ra.REALM_ID
WHERE ra.NAME LIKE '%theme%' 
   OR ra.NAME LIKE '%style%' 
   OR ra.NAME LIKE '%css%'
   OR ra.NAME LIKE '%login%'
ORDER BY r.NAME, ra.NAME;

-- Check if there are any client-specific theme overrides
SELECT 
    c.CLIENT_ID,
    ca.NAME as ATTRIBUTE_NAME,
    ca.VALUE as ATTRIBUTE_VALUE
FROM CLIENT c
LEFT JOIN CLIENT_ATTRIBUTES ca ON c.ID = ca.CLIENT_ID
WHERE ca.NAME LIKE '%theme%' 
   OR ca.NAME LIKE '%style%' 
   OR ca.NAME LIKE '%css%'
ORDER BY c.CLIENT_ID, ca.NAME;
