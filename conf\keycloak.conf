# Basic settings for running in production. Change accordingly before deploying the server.

# Database

# The database vendor.
db=mssql

# The username of the database user.
db-username=sa
#db-username=timur

# The password of the database user.
db-password=M1t1g@t0r
#db-password=T!mur123

# The full database JDBC URL. If not provided, a default URL is set based on the selected database vendor.
db-url=******************************************************************************
#db-url=***************************************************************************

# Observability

# If the server should expose healthcheck endpoints.
#health-enabled=true

# If the server should expose metrics endpoints.
#metrics-enabled=true

# HTTP
https-port=9443

# The file path to a server certificate or certificate chain in PEM format.
#https-certificate-file=C:/Mobile Aspects/curr/conf/cch9appvm01_mobileaspectshealth_org_cert.pem
https-certificate-file=C:/Mobile Aspects/curr/conf/kc.crt.pem

# The file path to a private key in PEM format.
#https-certificate-key-file=C:/Mobile Aspects/curr/conf/cch9appvm01_mobileaspectshealth_org_key.pem
https-certificate-key-file=C:/Mobile Aspects/curr/conf/kc.key.pem

# The proxy address forwarding mode if the server is behind a reverse proxy.
#proxy=reencrypt

# Do not attach route to cookies and rely on the session affinity capabilities from reverse proxy
#spi-sticky-session-encoder-infinispan-should-attach-route=false

# Hostname for the Keycloak server.
#hostname=************
hostname=************
#hostname=**************

hostname-strict=false

#hostname-strict-https=false
