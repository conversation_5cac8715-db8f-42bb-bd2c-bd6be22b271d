.login-pf body {
    background: #152935;
    background-size: cover;
    height: 100%;
}

/* Custom logo styling */
.login-pf-page .login-pf-brand {
    margin-bottom: 30px;
    text-align: center;
}

.login-pf-page .login-pf-brand img {
    max-width: 200px;
    height: auto;
}

.login-pf-page .login-pf-page-header {
    margin-bottom: 0px;
}

/* Alternative: Add logo above the login form */
#kc-header-wrapper::before {
    content: "";
    display: block;
    width: 300px;
    height: 80px;
    margin: 0 auto 15px auto;
    background-image: url('../img/MobileAspects-Logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Style the header wrapper */
#kc-header-wrapper {
    text-align: center;
    margin-bottom: 20px;
}

/* Hide the username and password fields */
#kc-form-login {
    display: none;
}

#kc-social-providers {
  margin-top: 0 !important;
  padding-top: 0 !important;
  border-top: none !important;
  background: none !important;
}

/* Remove the visible line */
#kc-social-providers hr {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Remove "Or sign in with" text */
#kc-social-providers h2 {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Microsoft sign-in button styling - Enhanced specificity */
#kc-social-providers .zocial.microsoft,
#kc-social-providers .btn.microsoft,
#kc-social-providers a[href*="microsoft"],
#kc-social-providers ul li a.zocial.microsoft,
#kc-social-providers ul li a.btn.microsoft,
#kc-social-providers ul li a[href*="microsoft"],
.login-pf #kc-social-providers .zocial.microsoft,
.login-pf #kc-social-providers .btn.microsoft,
.login-pf #kc-social-providers a[href*="microsoft"],
body #kc-social-providers .zocial.microsoft,
body #kc-social-providers .btn.microsoft,
body #kc-social-providers a[href*="microsoft"] {
  background-color: #0066cc !important;
  background: #0066cc !important;
  color: white !important;
  border: none !important;
  border-color: #0066cc !important;
}

/* Microsoft sign-in button hover state - Enhanced specificity */
#kc-social-providers .zocial.microsoft:hover,
#kc-social-providers .btn.microsoft:hover,
#kc-social-providers a[href*="microsoft"]:hover,
#kc-social-providers ul li a.zocial.microsoft:hover,
#kc-social-providers ul li a.btn.microsoft:hover,
#kc-social-providers ul li a[href*="microsoft"]:hover,
.login-pf #kc-social-providers .zocial.microsoft:hover,
.login-pf #kc-social-providers .btn.microsoft:hover,
.login-pf #kc-social-providers a[href*="microsoft"]:hover,
body #kc-social-providers .zocial.microsoft:hover,
body #kc-social-providers .btn.microsoft:hover,
body #kc-social-providers a[href*="microsoft"]:hover {
  background-color: #004080 !important;
  background: #004080 !important;
  color: white !important;
  border-color: #004080 !important;
}

/* Additional Microsoft button selectors for maximum compatibility */
a.zocial.microsoft,
a.btn.microsoft,
a[href*="microsoft"],
.zocial.microsoft,
.btn.microsoft,
[class*="microsoft"],
.social-link[href*="microsoft"],
.social-button[href*="microsoft"] {
  background-color: #0066cc !important;
  background: #0066cc !important;
  color: white !important;
  border: none !important;
  border-color: #0066cc !important;
}

a.zocial.microsoft:hover,
a.btn.microsoft:hover,
a[href*="microsoft"]:hover,
.zocial.microsoft:hover,
.btn.microsoft:hover,
[class*="microsoft"]:hover,
.social-link[href*="microsoft"]:hover,
.social-button[href*="microsoft"]:hover {
  background-color: #004080 !important;
  background: #004080 !important;
  color: white !important;
  border-color: #004080 !important;
}